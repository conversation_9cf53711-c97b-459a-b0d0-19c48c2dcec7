import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import adminService from '../../services/adminService';
import toast from '../../utils/toast';
import '../../styles/AdminDashboard.css';

const AdminDashboard = () => {
  const [dashboardStats, setDashboardStats] = useState(null);
  const [userStats, setUserStats] = useState(null);
  const [contentStats, setContentStats] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch all dashboard data in parallel
      const [dashboardData, userData, contentData, activityData] = await Promise.all([
        adminService.getDashboardStats(),
        adminService.getUserStats(),
        adminService.getContentStats(),
        adminService.getRecentActivity()
      ]);

      setDashboardStats(dashboardData.data);
      setUserStats(userData.data);
      setContentStats(contentData.data);
      setRecentActivity(activityData.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-dashboard">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <h1>Admin Dashboard</h1>
        <p>Welcome to the XO Sports Hub administration panel</p>
      </div>

      {/* Quick Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{userStats?.totalUsers || 0}</h3>
            <p>Total Users</p>
            <span className="stat-change">+{userStats?.newUsers || 0} this month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📚</div>
          <div className="stat-content">
            <h3>{contentStats?.totalContent || 0}</h3>
            <p>Total Content</p>
            <span className="stat-change">+{contentStats?.recentContent || 0} this month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>${dashboardStats?.totalRevenue || 0}</h3>
            <p>Total Revenue</p>
            <span className="stat-change">+{dashboardStats?.monthlyGrowth || 0}%</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <h3>{dashboardStats?.totalOrders || 0}</h3>
            <p>Total Orders</p>
            <span className="stat-change">{dashboardStats?.pendingOrders || 0} pending</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="actions-grid">
          <Link to="/admin/users" className="action-card">
            <div className="action-icon">👥</div>
            <h3>Manage Users</h3>
            <p>View, edit, and manage user accounts</p>
          </Link>

          <Link to="/admin/content" className="action-card">
            <div className="action-icon">📚</div>
            <h3>Content Management</h3>
            <p>Review and moderate content</p>
          </Link>

          <Link to="/admin/orders" className="action-card">
            <div className="action-icon">📦</div>
            <h3>Order Management</h3>
            <p>View and manage orders</p>
          </Link>

          <Link to="/admin/settings" className="action-card">
            <div className="action-icon">⚙️</div>
            <h3>System Settings</h3>
            <p>Configure platform settings</p>
          </Link>

          <Link to="/admin/analytics" className="action-card">
            <div className="action-icon">📊</div>
            <h3>Analytics</h3>
            <p>View detailed analytics and reports</p>
          </Link>

          <Link to="/admin/cms" className="action-card">
            <div className="action-icon">📝</div>
            <h3>CMS Pages</h3>
            <p>Manage website content pages</p>
          </Link>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="recent-activity">
        <h2>Recent Activity</h2>
        <div className="activity-list">
          {recentActivity.length > 0 ? (
            recentActivity.slice(0, 10).map((activity, index) => (
              <div key={index} className="activity-item">
                <div className="activity-icon">
                  {activity.type === 'user' && '👤'}
                  {activity.type === 'order' && '📦'}
                  {activity.type === 'content' && '📚'}
                  {activity.type === 'payment' && '💰'}
                </div>
                <div className="activity-content">
                  <p>{activity.description}</p>
                  <span className="activity-time">{activity.timestamp}</span>
                </div>
              </div>
            ))
          ) : (
            <p className="no-activity">No recent activity</p>
          )}
        </div>
      </div>

      {/* System Status */}
      <div className="system-status">
        <h2>System Status</h2>
        <div className="status-grid">
          <div className="status-item">
            <div className="status-indicator green"></div>
            <span>Database</span>
            <span className="status-value">Online</span>
          </div>
          <div className="status-item">
            <div className="status-indicator green"></div>
            <span>Payment Gateway</span>
            <span className="status-value">Active</span>
          </div>
          <div className="status-item">
            <div className="status-indicator green"></div>
            <span>File Storage</span>
            <span className="status-value">Available</span>
          </div>
          <div className="status-item">
            <div className="status-indicator yellow"></div>
            <span>Email Service</span>
            <span className="status-value">Limited</span>
          </div>
        </div>
      </div>

      {/* Content Overview */}
      {contentStats && (
        <div className="content-overview">
          <h2>Content Overview</h2>
          <div className="overview-grid">
            <div className="overview-item">
              <h4>Published</h4>
              <span className="overview-number">{contentStats.statusDistribution?.published || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Draft</h4>
              <span className="overview-number">{contentStats.statusDistribution?.draft || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Archived</h4>
              <span className="overview-number">{contentStats.statusDistribution?.archived || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Fixed Price</h4>
              <span className="overview-number">{contentStats.saleTypeDistribution?.fixedPrice || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Auction</h4>
              <span className="overview-number">{contentStats.saleTypeDistribution?.auction || 0}</span>
            </div>
          </div>
        </div>
      )}

      {/* User Overview */}
      {userStats && (
        <div className="user-overview">
          <h2>User Overview</h2>
          <div className="overview-grid">
            <div className="overview-item">
              <h4>Buyers</h4>
              <span className="overview-number">{userStats.roleDistribution?.buyers || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Sellers</h4>
              <span className="overview-number">{userStats.roleDistribution?.sellers || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Verified</h4>
              <span className="overview-number">{userStats.verificationStatus?.verified || 0}</span>
            </div>
            <div className="overview-item">
              <h4>Active Users</h4>
              <span className="overview-number">{userStats.activeUsers || 0}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
