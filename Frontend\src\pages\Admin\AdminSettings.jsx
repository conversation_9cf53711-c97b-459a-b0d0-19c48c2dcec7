import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import adminService from '../../services/adminService';
import toast from '../../utils/toast';
import '../../styles/AdminSettings.css';

const AdminSettings = () => {
  const [settings, setSettings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeGroup, setActiveGroup] = useState('general');
  const [editingSettings, setEditingSettings] = useState({});
  const [newSetting, setNewSetting] = useState({
    key: '',
    value: '',
    group: 'general',
    description: '',
    isPublic: false
  });
  const [showAddForm, setShowAddForm] = useState(false);

  const settingGroups = [
    { id: 'general', name: 'General', icon: '⚙️' },
    { id: 'payment', name: 'Payment', icon: '💳' },
    { id: 'email', name: 'Email', icon: '📧' },
    { id: 'content', name: 'Content', icon: '📚' },
    { id: 'user', name: 'User', icon: '👥' }
  ];

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await adminService.getSettings();
      setSettings(response.data || []);
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (settingId, field, value) => {
    setEditingSettings(prev => ({
      ...prev,
      [settingId]: {
        ...prev[settingId],
        [field]: value
      }
    }));
  };

  const handleSaveSetting = async (settingId) => {
    const editedSetting = editingSettings[settingId];
    if (!editedSetting) return;

    try {
      const originalSetting = settings.find(s => s._id === settingId);
      const updates = {};
      
      // Only include changed fields
      if (editedSetting.value !== undefined && editedSetting.value !== originalSetting.value) {
        updates.value = editedSetting.value;
      }
      if (editedSetting.description !== undefined && editedSetting.description !== originalSetting.description) {
        updates.description = editedSetting.description;
      }
      if (editedSetting.isPublic !== undefined && editedSetting.isPublic !== originalSetting.isPublic) {
        updates.isPublic = editedSetting.isPublic;
      }

      if (Object.keys(updates).length === 0) {
        toast.info('No changes to save');
        return;
      }

      await adminService.updateSettings({ [originalSetting.key]: updates.value });
      toast.success('Setting updated successfully');
      
      // Remove from editing state
      setEditingSettings(prev => {
        const newState = { ...prev };
        delete newState[settingId];
        return newState;
      });
      
      fetchSettings();
    } catch (error) {
      toast.error('Failed to update setting');
    }
  };

  const handleCancelEdit = (settingId) => {
    setEditingSettings(prev => {
      const newState = { ...prev };
      delete newState[settingId];
      return newState;
    });
  };

  const handleDeleteSetting = async (settingId) => {
    if (window.confirm('Are you sure you want to delete this setting?')) {
      try {
        await adminService.deleteSetting(settingId);
        toast.success('Setting deleted successfully');
        fetchSettings();
      } catch (error) {
        toast.error('Failed to delete setting');
      }
    }
  };

  const handleAddSetting = async () => {
    if (!newSetting.key || !newSetting.value) {
      toast.error('Key and value are required');
      return;
    }

    try {
      await adminService.createSetting(newSetting);
      toast.success('Setting created successfully');
      setNewSetting({
        key: '',
        value: '',
        group: 'general',
        description: '',
        isPublic: false
      });
      setShowAddForm(false);
      fetchSettings();
    } catch (error) {
      toast.error('Failed to create setting');
    }
  };

  const getSettingsByGroup = (group) => {
    return settings.filter(setting => setting.group === group);
  };

  const renderSettingValue = (setting) => {
    const isEditing = editingSettings[setting._id];
    const currentValue = isEditing?.value !== undefined ? isEditing.value : setting.value;

    // Handle different value types
    if (typeof setting.value === 'boolean') {
      return (
        <select
          value={currentValue.toString()}
          onChange={(e) => handleSettingChange(setting._id, 'value', e.target.value === 'true')}
          className="setting-input"
          disabled={!isEditing}
        >
          <option value="true">True</option>
          <option value="false">False</option>
        </select>
      );
    }

    if (typeof setting.value === 'number') {
      return (
        <input
          type="number"
          value={currentValue}
          onChange={(e) => handleSettingChange(setting._id, 'value', parseFloat(e.target.value))}
          className="setting-input"
          disabled={!isEditing}
        />
      );
    }

    // String values
    return (
      <textarea
        value={currentValue}
        onChange={(e) => handleSettingChange(setting._id, 'value', e.target.value)}
        className="setting-input"
        disabled={!isEditing}
        rows={2}
      />
    );
  };

  if (loading) {
    return (
      <div className="admin-settings">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-settings">
      <div className="admin-header">
        <h1>System Settings</h1>
        <div className="header-actions">
          <button 
            onClick={() => setShowAddForm(true)}
            className="btn-add-setting"
          >
            Add Setting
          </button>
          <Link to="/admin" className="back-link">← Back to Dashboard</Link>
        </div>
      </div>

      <div className="settings-container">
        {/* Settings Groups Sidebar */}
        <div className="settings-sidebar">
          <h3>Setting Groups</h3>
          <div className="group-list">
            {settingGroups.map(group => (
              <button
                key={group.id}
                className={`group-item ${activeGroup === group.id ? 'active' : ''}`}
                onClick={() => setActiveGroup(group.id)}
              >
                <span className="group-icon">{group.icon}</span>
                <span className="group-name">{group.name}</span>
                <span className="group-count">
                  {getSettingsByGroup(group.id).length}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Settings Content */}
        <div className="settings-content">
          <div className="settings-header">
            <h2>
              {settingGroups.find(g => g.id === activeGroup)?.icon} {' '}
              {settingGroups.find(g => g.id === activeGroup)?.name} Settings
            </h2>
          </div>

          <div className="settings-list">
            {getSettingsByGroup(activeGroup).map(setting => (
              <div key={setting._id} className="setting-item">
                <div className="setting-header">
                  <div className="setting-info">
                    <h4>{setting.key}</h4>
                    {setting.description && (
                      <p className="setting-description">{setting.description}</p>
                    )}
                  </div>
                  <div className="setting-badges">
                    {setting.isPublic && (
                      <span className="badge public">Public</span>
                    )}
                    <span className="badge type">
                      {typeof setting.value}
                    </span>
                  </div>
                </div>

                <div className="setting-value">
                  {renderSettingValue(setting)}
                </div>

                <div className="setting-actions">
                  {editingSettings[setting._id] ? (
                    <>
                      <button 
                        onClick={() => handleSaveSetting(setting._id)}
                        className="btn-save"
                      >
                        Save
                      </button>
                      <button 
                        onClick={() => handleCancelEdit(setting._id)}
                        className="btn-cancel"
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <>
                      <button 
                        onClick={() => setEditingSettings(prev => ({
                          ...prev,
                          [setting._id]: {
                            value: setting.value,
                            description: setting.description,
                            isPublic: setting.isPublic
                          }
                        }))}
                        className="btn-edit"
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => handleDeleteSetting(setting._id)}
                        className="btn-delete"
                      >
                        Delete
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}

            {getSettingsByGroup(activeGroup).length === 0 && (
              <div className="no-settings">
                <p>No settings found in this group.</p>
                <button 
                  onClick={() => setShowAddForm(true)}
                  className="btn-add-first"
                >
                  Add First Setting
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add Setting Modal */}
      {showAddForm && (
        <div className="modal-overlay" onClick={() => setShowAddForm(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Add New Setting</h2>
              <button 
                onClick={() => setShowAddForm(false)}
                className="modal-close"
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>Setting Key</label>
                <input
                  type="text"
                  value={newSetting.key}
                  onChange={(e) => setNewSetting(prev => ({ ...prev, key: e.target.value }))}
                  placeholder="e.g., platform_commission_rate"
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>Value</label>
                <textarea
                  value={newSetting.value}
                  onChange={(e) => setNewSetting(prev => ({ ...prev, value: e.target.value }))}
                  placeholder="Setting value"
                  className="form-input"
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label>Group</label>
                <select
                  value={newSetting.group}
                  onChange={(e) => setNewSetting(prev => ({ ...prev, group: e.target.value }))}
                  className="form-input"
                >
                  {settingGroups.map(group => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={newSetting.description}
                  onChange={(e) => setNewSetting(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Optional description"
                  className="form-input"
                  rows={2}
                />
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={newSetting.isPublic}
                    onChange={(e) => setNewSetting(prev => ({ ...prev, isPublic: e.target.checked }))}
                  />
                  Public Setting (accessible to frontend)
                </label>
              </div>

              <div className="modal-actions">
                <button onClick={handleAddSetting} className="btn-create">
                  Create Setting
                </button>
                <button onClick={() => setShowAddForm(false)} className="btn-cancel">
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSettings;
