import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import adminService from '../../services/adminService';
import toast from '../../utils/toast';
import '../../styles/AdminContent.css';

const AdminContent = () => {
  const [content, setContent] = useState([]);
  const [pendingContent, setPendingContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedContent, setSelectedContent] = useState([]);

  useEffect(() => {
    fetchContent();
  }, [activeTab, currentPage]);

  const fetchContent = async () => {
    try {
      setLoading(true);
      
      if (activeTab === 'pending') {
        const response = await adminService.getPendingContent({ 
          page: currentPage, 
          limit: 20 
        });
        setPendingContent(response.data);
        setTotalPages(response.pages);
      } else {
        // Fetch all content - this would need to be implemented in the backend
        // For now, we'll use the pending content endpoint as a placeholder
        const response = await adminService.getPendingContent({ 
          page: currentPage, 
          limit: 20 
        });
        setContent(response.data);
        setTotalPages(response.pages);
      }
    } catch (error) {
      console.error('Error fetching content:', error);
      toast.error('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveContent = async (contentId) => {
    try {
      await adminService.approveContent(contentId);
      toast.success('Content approved successfully');
      fetchContent();
    } catch (error) {
      toast.error('Failed to approve content');
    }
  };

  const handleRejectContent = async (contentId) => {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason) {
      try {
        await adminService.rejectContent(contentId, reason);
        toast.success('Content rejected successfully');
        fetchContent();
      } catch (error) {
        toast.error('Failed to reject content');
      }
    }
  };

  const handleFeatureContent = async (contentId, featured) => {
    try {
      await adminService.featureContent(contentId, featured);
      toast.success(`Content ${featured ? 'featured' : 'unfeatured'} successfully`);
      fetchContent();
    } catch (error) {
      toast.error('Failed to update content');
    }
  };

  const handleBulkUpdate = async (updates) => {
    if (selectedContent.length === 0) return;

    try {
      await adminService.bulkUpdateContent(selectedContent, updates);
      toast.success(`Updated ${selectedContent.length} content items successfully`);
      setSelectedContent([]);
      fetchContent();
    } catch (error) {
      toast.error('Failed to update content');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const currentContent = activeTab === 'pending' ? pendingContent : content;

  if (loading) {
    return (
      <div className="admin-content">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-content">
      <div className="admin-header">
        <h1>Content Management</h1>
        <Link to="/admin" className="back-link">← Back to Dashboard</Link>
      </div>

      {/* Tabs */}
      <div className="content-tabs">
        <button 
          className={`tab ${activeTab === 'all' ? 'active' : ''}`}
          onClick={() => setActiveTab('all')}
        >
          All Content
        </button>
        <button 
          className={`tab ${activeTab === 'pending' ? 'active' : ''}`}
          onClick={() => setActiveTab('pending')}
        >
          Pending Review
        </button>
        <button 
          className={`tab ${activeTab === 'reported' ? 'active' : ''}`}
          onClick={() => setActiveTab('reported')}
        >
          Reported Content
        </button>
      </div>

      {/* Bulk Actions */}
      {selectedContent.length > 0 && (
        <div className="bulk-actions">
          <span>{selectedContent.length} items selected</span>
          <div className="bulk-buttons">
            <button 
              onClick={() => handleBulkUpdate({ status: 'Published' })}
              className="bulk-btn approve"
            >
              Approve Selected
            </button>
            <button 
              onClick={() => handleBulkUpdate({ status: 'Archived' })}
              className="bulk-btn reject"
            >
              Archive Selected
            </button>
            <button 
              onClick={() => handleBulkUpdate({ isFeatured: true })}
              className="bulk-btn feature"
            >
              Feature Selected
            </button>
            <button 
              onClick={() => setSelectedContent([])}
              className="bulk-btn cancel"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Content Grid */}
      <div className="content-grid">
        {currentContent.map(item => (
          <div key={item._id} className="content-card">
            <div className="content-header">
              <input
                type="checkbox"
                checked={selectedContent.includes(item._id)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedContent(prev => [...prev, item._id]);
                  } else {
                    setSelectedContent(prev => prev.filter(id => id !== item._id));
                  }
                }}
              />
              <span className={`status-badge ${item.status?.toLowerCase()}`}>
                {item.status}
              </span>
            </div>

            <div className="content-image">
              {item.thumbnail ? (
                <img src={item.thumbnail} alt={item.title} />
              ) : (
                <div className="placeholder-image">📚</div>
              )}
            </div>

            <div className="content-info">
              <h3>{item.title}</h3>
              <p className="content-description">{item.description}</p>
              
              <div className="content-meta">
                <span className="price">{formatPrice(item.price)}</span>
                <span className="category">{item.category}</span>
                <span className="sale-type">{item.saleType}</span>
              </div>

              <div className="content-seller">
                <span>By: {item.seller?.firstName} {item.seller?.lastName}</span>
                <span className="created-date">{formatDate(item.createdAt)}</span>
              </div>
            </div>

            <div className="content-actions">
              {item.status === 'Draft' && (
                <>
                  <button 
                    onClick={() => handleApproveContent(item._id)}
                    className="btn-approve"
                  >
                    Approve
                  </button>
                  <button 
                    onClick={() => handleRejectContent(item._id)}
                    className="btn-reject"
                  >
                    Reject
                  </button>
                </>
              )}
              
              <button 
                onClick={() => handleFeatureContent(item._id, !item.isFeatured)}
                className={`btn-feature ${item.isFeatured ? 'featured' : ''}`}
              >
                {item.isFeatured ? 'Unfeature' : 'Feature'}
              </button>
              
              <Link to={`/admin/content/${item._id}`} className="btn-view">
                View Details
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="pagination-btn"
          >
            Previous
          </button>
          
          <span className="pagination-info">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="pagination-btn"
          >
            Next
          </button>
        </div>
      )}

      {currentContent.length === 0 && (
        <div className="no-content">
          <p>No content found for the selected tab.</p>
        </div>
      )}
    </div>
  );
};

export default AdminContent;
