import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import adminService from '../../services/adminService';
import toast from '../../utils/toast';
import '../../styles/AdminOrders.css';

const AdminOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState('');
  const [filterType, setFilterType] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [showOrderDetails, setShowOrderDetails] = useState(null);

  useEffect(() => {
    fetchOrders();
  }, [currentPage, filterStatus, filterType, searchTerm]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 20,
        ...(filterStatus && { paymentStatus: filterStatus }),
        ...(filterType && { orderType: filterType }),
        ...(searchTerm && { search: searchTerm })
      };

      const response = await adminService.getAllOrders(params);
      setOrders(response.data);
      setTotalPages(response.pages || 1);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateOrderStatus = async (orderId, status) => {
    try {
      await adminService.updateOrder(orderId, { paymentStatus: status });
      toast.success('Order status updated successfully');
      fetchOrders();
    } catch (error) {
      toast.error('Failed to update order status');
    }
  };

  const handleRefundOrder = async (orderId) => {
    if (window.confirm('Are you sure you want to process a refund for this order?')) {
      try {
        await adminService.updateOrder(orderId, { paymentStatus: 'Refunded' });
        toast.success('Refund processed successfully');
        fetchOrders();
      } catch (error) {
        toast.error('Failed to process refund');
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'Pending': return 'warning';
      case 'Failed': return 'error';
      case 'Refunded': return 'info';
      case 'Expired': return 'expired';
      default: return 'default';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'Fixed': return 'fixed';
      case 'Auction': return 'auction';
      case 'Custom': return 'custom';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="admin-orders">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-orders">
      <div className="admin-header">
        <h1>Order Management</h1>
        <Link to="/admin" className="back-link">← Back to Dashboard</Link>
      </div>

      {/* Filters and Search */}
      <div className="filters-section">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search orders by ID, buyer, or seller..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filters">
          <select 
            value={filterStatus} 
            onChange={(e) => setFilterStatus(e.target.value)} 
            className="filter-select"
          >
            <option value="">All Statuses</option>
            <option value="Pending">Pending</option>
            <option value="Completed">Completed</option>
            <option value="Failed">Failed</option>
            <option value="Refunded">Refunded</option>
            <option value="Expired">Expired</option>
          </select>

          <select 
            value={filterType} 
            onChange={(e) => setFilterType(e.target.value)} 
            className="filter-select"
          >
            <option value="">All Types</option>
            <option value="Fixed">Fixed Price</option>
            <option value="Auction">Auction</option>
            <option value="Custom">Custom Request</option>
          </select>
        </div>
      </div>

      {/* Orders Table */}
      <div className="orders-table-container">
        <table className="orders-table">
          <thead>
            <tr>
              <th>Order ID</th>
              <th>Buyer</th>
              <th>Seller</th>
              <th>Content</th>
              <th>Type</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {orders.map(order => (
              <tr key={order._id}>
                <td>
                  <span className="order-id">#{order._id.slice(-8)}</span>
                </td>
                <td>
                  <div className="user-info">
                    <div className="user-name">
                      {order.buyer?.firstName} {order.buyer?.lastName}
                    </div>
                    <div className="user-email">{order.buyer?.email}</div>
                  </div>
                </td>
                <td>
                  <div className="user-info">
                    <div className="user-name">
                      {order.seller?.firstName} {order.seller?.lastName}
                    </div>
                    <div className="user-email">{order.seller?.email}</div>
                  </div>
                </td>
                <td>
                  <div className="content-info">
                    <div className="content-title">{order.content?.title}</div>
                    <div className="content-category">{order.content?.category}</div>
                  </div>
                </td>
                <td>
                  <span className={`type-badge ${getTypeColor(order.orderType)}`}>
                    {order.orderType}
                  </span>
                </td>
                <td>
                  <div className="amount-info">
                    <div className="total-amount">{formatPrice(order.totalAmount)}</div>
                    <div className="platform-fee">Fee: {formatPrice(order.platformFee)}</div>
                  </div>
                </td>
                <td>
                  <span className={`status-badge ${getStatusColor(order.paymentStatus)}`}>
                    {order.paymentStatus}
                  </span>
                </td>
                <td>{formatDate(order.createdAt)}</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      onClick={() => setShowOrderDetails(order)}
                      className="btn-view"
                    >
                      View
                    </button>
                    
                    {order.paymentStatus === 'Pending' && (
                      <select
                        onChange={(e) => handleUpdateOrderStatus(order._id, e.target.value)}
                        className="status-select"
                        defaultValue=""
                      >
                        <option value="" disabled>Update Status</option>
                        <option value="Completed">Mark Completed</option>
                        <option value="Failed">Mark Failed</option>
                        <option value="Expired">Mark Expired</option>
                      </select>
                    )}
                    
                    {order.paymentStatus === 'Completed' && (
                      <button 
                        onClick={() => handleRefundOrder(order._id)}
                        className="btn-refund"
                      >
                        Refund
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="pagination-btn"
          >
            Previous
          </button>
          
          <span className="pagination-info">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="pagination-btn"
          >
            Next
          </button>
        </div>
      )}

      {/* Order Details Modal */}
      {showOrderDetails && (
        <div className="modal-overlay" onClick={() => setShowOrderDetails(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Order Details</h2>
              <button 
                onClick={() => setShowOrderDetails(null)}
                className="modal-close"
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="detail-section">
                <h3>Order Information</h3>
                <div className="detail-grid">
                  <div className="detail-item">
                    <label>Order ID:</label>
                    <span>#{showOrderDetails._id}</span>
                  </div>
                  <div className="detail-item">
                    <label>Type:</label>
                    <span>{showOrderDetails.orderType}</span>
                  </div>
                  <div className="detail-item">
                    <label>Status:</label>
                    <span className={`status-badge ${getStatusColor(showOrderDetails.paymentStatus)}`}>
                      {showOrderDetails.paymentStatus}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>Created:</label>
                    <span>{formatDate(showOrderDetails.createdAt)}</span>
                  </div>
                </div>
              </div>

              <div className="detail-section">
                <h3>Financial Details</h3>
                <div className="detail-grid">
                  <div className="detail-item">
                    <label>Content Price:</label>
                    <span>{formatPrice(showOrderDetails.amount)}</span>
                  </div>
                  <div className="detail-item">
                    <label>Platform Fee:</label>
                    <span>{formatPrice(showOrderDetails.platformFee)}</span>
                  </div>
                  <div className="detail-item">
                    <label>Seller Earnings:</label>
                    <span>{formatPrice(showOrderDetails.sellerEarnings)}</span>
                  </div>
                  <div className="detail-item">
                    <label>Total Amount:</label>
                    <span className="total-highlight">{formatPrice(showOrderDetails.totalAmount)}</span>
                  </div>
                </div>
              </div>

              <div className="detail-section">
                <h3>Payment Information</h3>
                <div className="detail-grid">
                  <div className="detail-item">
                    <label>Payment Method:</label>
                    <span>{showOrderDetails.paymentMethod || 'N/A'}</span>
                  </div>
                  <div className="detail-item">
                    <label>Payment Intent ID:</label>
                    <span className="payment-id">{showOrderDetails.paymentIntentId || 'N/A'}</span>
                  </div>
                  {showOrderDetails.cardDetails && (
                    <>
                      <div className="detail-item">
                        <label>Card Type:</label>
                        <span>{showOrderDetails.cardDetails.cardType}</span>
                      </div>
                      <div className="detail-item">
                        <label>Last 4 Digits:</label>
                        <span>****{showOrderDetails.cardDetails.lastFourDigits}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {orders.length === 0 && (
        <div className="no-orders">
          <p>No orders found matching your criteria.</p>
        </div>
      )}
    </div>
  );
};

export default AdminOrders;
