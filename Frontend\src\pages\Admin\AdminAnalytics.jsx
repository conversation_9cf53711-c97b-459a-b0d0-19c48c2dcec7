import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import adminService from '../../services/adminService';
import toast from '../../utils/toast';
import '../../styles/AdminAnalytics.css';

const AdminAnalytics = () => {
  const [analytics, setAnalytics] = useState({
    dashboard: null,
    users: null,
    content: null,
    orders: null,
    revenue: null
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('30d');

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'users', name: 'Users', icon: '👥' },
    { id: 'content', name: 'Content', icon: '📚' },
    { id: 'orders', name: 'Orders', icon: '📦' },
    { id: 'revenue', name: 'Revenue', icon: '💰' }
  ];

  const dateRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' }
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Fetch all analytics data in parallel
      const [dashboardData, userData, contentData, orderData, revenueData] = await Promise.all([
        adminService.getDashboardStats(),
        adminService.getUserAnalytics(),
        adminService.getContentAnalytics(),
        adminService.getOrderAnalytics(),
        adminService.getRevenueAnalytics()
      ]);

      setAnalytics({
        dashboard: dashboardData.data,
        users: userData.data,
        content: contentData.data,
        orders: orderData.data,
        revenue: revenueData.data
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toString() || '0';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  const renderOverviewTab = () => (
    <div className="analytics-overview">
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-icon">👥</div>
          <div className="metric-content">
            <h3>{formatNumber(analytics.dashboard?.totalUsers)}</h3>
            <p>Total Users</p>
            <span className="metric-change positive">
              +{analytics.dashboard?.userGrowth || 0}% this month
            </span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">📚</div>
          <div className="metric-content">
            <h3>{formatNumber(analytics.dashboard?.totalContent)}</h3>
            <p>Total Content</p>
            <span className="metric-change positive">
              +{analytics.dashboard?.contentGrowth || 0}% this month
            </span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">📦</div>
          <div className="metric-content">
            <h3>{formatNumber(analytics.dashboard?.totalOrders)}</h3>
            <p>Total Orders</p>
            <span className="metric-change positive">
              +{analytics.dashboard?.orderGrowth || 0}% this month
            </span>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">💰</div>
          <div className="metric-content">
            <h3>{formatCurrency(analytics.dashboard?.totalRevenue)}</h3>
            <p>Total Revenue</p>
            <span className="metric-change positive">
              +{analytics.dashboard?.revenueGrowth || 0}% this month
            </span>
          </div>
        </div>
      </div>

      <div className="charts-grid">
        <div className="chart-card">
          <h3>User Growth Trend</h3>
          <div className="chart-placeholder">
            <p>Chart visualization would go here</p>
            <small>Integration with charting library needed</small>
          </div>
        </div>

        <div className="chart-card">
          <h3>Revenue Trend</h3>
          <div className="chart-placeholder">
            <p>Chart visualization would go here</p>
            <small>Integration with charting library needed</small>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUsersTab = () => (
    <div className="analytics-users">
      <div className="stats-grid">
        <div className="stat-item">
          <h4>Total Users</h4>
          <span className="stat-value">{formatNumber(analytics.users?.totalUsers)}</span>
        </div>
        <div className="stat-item">
          <h4>Active Users</h4>
          <span className="stat-value">{formatNumber(analytics.users?.activeUsers)}</span>
        </div>
        <div className="stat-item">
          <h4>New Users</h4>
          <span className="stat-value">{formatNumber(analytics.users?.newUsers)}</span>
        </div>
        <div className="stat-item">
          <h4>Verified Users</h4>
          <span className="stat-value">{formatNumber(analytics.users?.verifiedUsers)}</span>
        </div>
      </div>

      <div className="breakdown-section">
        <h3>User Role Distribution</h3>
        <div className="breakdown-grid">
          <div className="breakdown-item">
            <span className="breakdown-label">Buyers</span>
            <div className="breakdown-bar">
              <div 
                className="breakdown-fill buyer" 
                style={{ width: `${(analytics.users?.buyers / analytics.users?.totalUsers * 100) || 0}%` }}
              ></div>
            </div>
            <span className="breakdown-value">{analytics.users?.buyers || 0}</span>
          </div>
          <div className="breakdown-item">
            <span className="breakdown-label">Sellers</span>
            <div className="breakdown-bar">
              <div 
                className="breakdown-fill seller" 
                style={{ width: `${(analytics.users?.sellers / analytics.users?.totalUsers * 100) || 0}%` }}
              ></div>
            </div>
            <span className="breakdown-value">{analytics.users?.sellers || 0}</span>
          </div>
          <div className="breakdown-item">
            <span className="breakdown-label">Admins</span>
            <div className="breakdown-bar">
              <div 
                className="breakdown-fill admin" 
                style={{ width: `${(analytics.users?.admins / analytics.users?.totalUsers * 100) || 0}%` }}
              ></div>
            </div>
            <span className="breakdown-value">{analytics.users?.admins || 0}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContentTab = () => (
    <div className="analytics-content">
      <div className="stats-grid">
        <div className="stat-item">
          <h4>Total Content</h4>
          <span className="stat-value">{formatNumber(analytics.content?.totalContent)}</span>
        </div>
        <div className="stat-item">
          <h4>Published</h4>
          <span className="stat-value">{formatNumber(analytics.content?.publishedContent)}</span>
        </div>
        <div className="stat-item">
          <h4>Pending Review</h4>
          <span className="stat-value">{formatNumber(analytics.content?.pendingContent)}</span>
        </div>
        <div className="stat-item">
          <h4>Featured</h4>
          <span className="stat-value">{formatNumber(analytics.content?.featuredContent)}</span>
        </div>
      </div>

      <div className="breakdown-section">
        <h3>Content by Category</h3>
        <div className="category-list">
          {analytics.content?.topCategories?.map((category, index) => (
            <div key={index} className="category-item">
              <span className="category-name">{category._id || 'Uncategorized'}</span>
              <span className="category-count">{category.count}</span>
            </div>
          )) || <p>No category data available</p>}
        </div>
      </div>
    </div>
  );

  const renderOrdersTab = () => (
    <div className="analytics-orders">
      <div className="stats-grid">
        <div className="stat-item">
          <h4>Total Orders</h4>
          <span className="stat-value">{formatNumber(analytics.orders?.totalOrders)}</span>
        </div>
        <div className="stat-item">
          <h4>Completed</h4>
          <span className="stat-value">{formatNumber(analytics.orders?.completedOrders)}</span>
        </div>
        <div className="stat-item">
          <h4>Pending</h4>
          <span className="stat-value">{formatNumber(analytics.orders?.pendingOrders)}</span>
        </div>
        <div className="stat-item">
          <h4>Success Rate</h4>
          <span className="stat-value">
            {formatPercentage((analytics.orders?.completedOrders / analytics.orders?.totalOrders * 100))}
          </span>
        </div>
      </div>

      <div className="breakdown-section">
        <h3>Order Types</h3>
        <div className="breakdown-grid">
          <div className="breakdown-item">
            <span className="breakdown-label">Fixed Price</span>
            <div className="breakdown-bar">
              <div 
                className="breakdown-fill fixed" 
                style={{ width: `${(analytics.orders?.fixedOrders / analytics.orders?.totalOrders * 100) || 0}%` }}
              ></div>
            </div>
            <span className="breakdown-value">{analytics.orders?.fixedOrders || 0}</span>
          </div>
          <div className="breakdown-item">
            <span className="breakdown-label">Auction</span>
            <div className="breakdown-bar">
              <div 
                className="breakdown-fill auction" 
                style={{ width: `${(analytics.orders?.auctionOrders / analytics.orders?.totalOrders * 100) || 0}%` }}
              ></div>
            </div>
            <span className="breakdown-value">{analytics.orders?.auctionOrders || 0}</span>
          </div>
          <div className="breakdown-item">
            <span className="breakdown-label">Custom</span>
            <div className="breakdown-bar">
              <div 
                className="breakdown-fill custom" 
                style={{ width: `${(analytics.orders?.customOrders / analytics.orders?.totalOrders * 100) || 0}%` }}
              ></div>
            </div>
            <span className="breakdown-value">{analytics.orders?.customOrders || 0}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderRevenueTab = () => (
    <div className="analytics-revenue">
      <div className="stats-grid">
        <div className="stat-item">
          <h4>Total Revenue</h4>
          <span className="stat-value">{formatCurrency(analytics.revenue?.totalRevenue)}</span>
        </div>
        <div className="stat-item">
          <h4>Platform Fees</h4>
          <span className="stat-value">{formatCurrency(analytics.revenue?.platformFees)}</span>
        </div>
        <div className="stat-item">
          <h4>Seller Earnings</h4>
          <span className="stat-value">{formatCurrency(analytics.revenue?.sellerEarnings)}</span>
        </div>
        <div className="stat-item">
          <h4>Average Order</h4>
          <span className="stat-value">{formatCurrency(analytics.revenue?.averageOrderValue)}</span>
        </div>
      </div>

      <div className="revenue-breakdown">
        <h3>Revenue Breakdown</h3>
        <div className="revenue-chart">
          <div className="revenue-item">
            <div className="revenue-bar">
              <div 
                className="revenue-fill platform" 
                style={{ width: `${(analytics.revenue?.platformFees / analytics.revenue?.totalRevenue * 100) || 0}%` }}
              ></div>
            </div>
            <div className="revenue-labels">
              <span>Platform Fees: {formatCurrency(analytics.revenue?.platformFees)}</span>
              <span>Seller Earnings: {formatCurrency(analytics.revenue?.sellerEarnings)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'overview': return renderOverviewTab();
      case 'users': return renderUsersTab();
      case 'content': return renderContentTab();
      case 'orders': return renderOrdersTab();
      case 'revenue': return renderRevenueTab();
      default: return renderOverviewTab();
    }
  };

  if (loading) {
    return (
      <div className="admin-analytics">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-analytics">
      <div className="admin-header">
        <h1>Analytics & Reports</h1>
        <div className="header-controls">
          <select 
            value={dateRange} 
            onChange={(e) => setDateRange(e.target.value)}
            className="date-range-select"
          >
            {dateRanges.map(range => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
          <Link to="/admin" className="back-link">← Back to Dashboard</Link>
        </div>
      </div>

      {/* Analytics Tabs */}
      <div className="analytics-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      {/* Analytics Content */}
      <div className="analytics-content">
        {renderActiveTab()}
      </div>
    </div>
  );
};

export default AdminAnalytics;
