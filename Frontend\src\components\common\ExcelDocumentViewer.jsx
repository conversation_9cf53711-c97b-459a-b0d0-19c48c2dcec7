import React, { useState, useEffect } from 'react';
import { FaDownload, FaExclamationTriangle, FaSync, FaFileExcel, FaTable } from 'react-icons/fa';
import { API_BASE_URL } from '../../utils/constants';
import '../../styles/ExcelDocumentViewer.css';

const ExcelDocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Excel Spreadsheet',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [documentData, setDocumentData] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [activeSheet, setActiveSheet] = useState(0);

  // Extract filename from fileUrl for API call
  const getFileName = () => {
    if (fileName) return fileName;
    if (fileUrl) return fileUrl.split('/').pop();
    return 'document.xlsx';
  };

  // Load document preview
  useEffect(() => {
    const loadDocumentPreview = async () => {
      if (!fileUrl) {
        setHasError(true);
        setErrorMessage('No file URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);

        const actualFileName = getFileName();
        console.log(`[ExcelViewer] Loading preview for: ${actualFileName}`);

        // Check if fileUrl is a JSON preview file
        if (fileUrl.endsWith('.json')) {
          console.log(`[ExcelViewer] Loading JSON preview from: ${fileUrl}`);

          // Fetch the JSON preview directly
          const response = await fetch(fileUrl);
          if (!response.ok) {
            throw new Error(`Failed to load preview: ${response.status}`);
          }

          const previewData = await response.json();
          if (previewData.success) {
            setDocumentData(previewData);
          } else {
            throw new Error(previewData.error || 'Invalid preview data');
          }
        } else {
          // Call our custom document preview API for live conversion
          const response = await fetch(`${API_BASE_URL}/document-preview/convert`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              fileUrl: fileUrl,
              fileName: actualFileName
            })
          });

          if (!response.ok) {
            throw new Error(`Preview generation failed: ${response.status}`);
          }

          const result = await response.json();

          if (!result.success) {
            throw new Error(result.message || 'Failed to generate preview');
          }

          setDocumentData(result.data);
        }

        console.log(`[ExcelViewer] Preview loaded successfully`);
        setIsLoading(false);

      } catch (error) {
        console.error('[ExcelViewer] Preview loading failed:', error);
        setHasError(true);
        setErrorMessage(error.message);
        setIsLoading(false);
      }
    };

    loadDocumentPreview();
  }, [fileUrl, fileName]);

  // Handle download - DISABLED FOR SECURITY
  const handleDownload = () => {
    console.warn('Download functionality has been disabled for security purposes');
  };

  // Get sheet names
  const getSheetNames = () => {
    if (!documentData?.sheets) return [];
    return Object.keys(documentData.sheets);
  };

  // Get active sheet data
  const getActiveSheetData = () => {
    const sheetNames = getSheetNames();
    if (sheetNames.length === 0) return null;

    const sheetName = sheetNames[activeSheet] || sheetNames[0];
    return documentData.sheets[sheetName];
  };

  // Render table cell
  const renderCell = (cell, rowIndex, colIndex) => {
    const cellValue = cell || '';
    const isHeader = rowIndex === 0;

    return (
      <td
        key={colIndex}
        className={`excel-table__cell ${isHeader ? 'excel-table__header-cell' : ''}`}
        title={cellValue}
      >
        {cellValue}
      </td>
    );
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className={`excel-document-viewer ${className}`} style={{ height }}>
        <div className="excel-document-viewer__header">
          <div className="excel-document-viewer__info">
            <span className="excel-document-viewer__title">{title}</span>
            <span className="excel-document-viewer__type">Microsoft Excel Spreadsheet</span>
          </div>
          {/* {showDownload && (
            <button 
              className="excel-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )} */}
        </div>

        <div className="excel-document-viewer__loading">
          <FaSync className="spinning" />
          <p>Converting Excel spreadsheet...</p>
          <p className="excel-document-viewer__loading-info">
            Processing sheets and data...
          </p>
        </div>
      </div>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <div className={`excel-document-viewer ${className}`} style={{ height }}>
        <div className="excel-document-viewer__header">
          <div className="excel-document-viewer__info">
            <span className="excel-document-viewer__title">{title}</span>
            <span className="excel-document-viewer__type">Microsoft Excel Spreadsheet</span>
          </div>
          {/* {showDownload && (
            <button 
              className="excel-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )} */}
        </div>

        <div className="excel-document-viewer__error">
          <FaExclamationTriangle />
          <h3>Preview Not Available</h3>
          <p>Unable to generate preview for this Excel spreadsheet.</p>
          <p className="excel-document-viewer__error-details">{errorMessage}</p>

          {showDownload && (
            <button
              className="excel-document-viewer__download-button"
              onClick={handleDownload}
            >
              <FaDownload />
              Download Excel Spreadsheet
            </button>
          )}
        </div>
      </div>
    );
  }

  const sheetNames = getSheetNames();
  const activeSheetData = getActiveSheetData();

  // Render document content
  return (
    <div className={`excel-document-viewer ${className}`} style={{ height }}>

      {/* Sheet tabs */}
      {sheetNames.length > 1 && (
        <div className="excel-document-viewer__sheet-tabs">
          {sheetNames.map((sheetName, index) => (
            <button
              key={index}
              className={`excel-sheet-tab ${index === activeSheet ? 'excel-sheet-tab--active' : ''}`}
              onClick={() => setActiveSheet(index)}
              title={sheetName}
            >
              <FaTable />
              {sheetName}
            </button>
          ))}
        </div>
      )}

      <div className="excel-document-viewer__content">
        {activeSheetData && activeSheetData.data.length > 0 ? (
          <div className="excel-table-container">
            <table className="excel-table">
              <tbody>
                {activeSheetData.data.map((row, rowIndex) => (
                  <tr key={rowIndex} className="excel-table__row">
                    {row.map((cell, colIndex) => renderCell(cell, rowIndex, colIndex))}
                  </tr>
                ))}
              </tbody>
            </table>


          </div>
        ) : (
          <div className="excel-document-viewer__no-content">
            <FaFileExcel />
            <p>No data found in this sheet</p>
            <p>The sheet may be empty or contain only formatting</p>
          </div>
        )}
      </div>

    </div>
  );
};

export default ExcelDocumentViewer;
