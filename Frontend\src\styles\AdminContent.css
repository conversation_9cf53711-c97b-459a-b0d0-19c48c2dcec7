.admin-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f8fafc;
  min-height: 100vh;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.admin-header h1 {
  color: #1a202c;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.back-link {
  color: #3182ce;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.back-link:hover {
  background-color: #e6f3ff;
  text-decoration: none;
}

/* Tabs */
.content-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.tab {
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: #4a5568;
}

.tab:hover {
  background: #f7fafc;
}

.tab.active {
  background: #3182ce;
  color: white;
}

/* Bulk Actions */
.bulk-actions {
  background: #3182ce;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bulk-buttons {
  display: flex;
  gap: 0.5rem;
}

.bulk-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bulk-btn.approve {
  background: #38a169;
  color: white;
}

.bulk-btn.approve:hover {
  background: #2f855a;
}

.bulk-btn.reject {
  background: #e53e3e;
  color: white;
}

.bulk-btn.reject:hover {
  background: #c53030;
}

.bulk-btn.feature {
  background: #d69e2e;
  color: white;
}

.bulk-btn.feature:hover {
  background: #b7791f;
}

.bulk-btn.cancel {
  background: #e2e8f0;
  color: #4a5568;
}

.bulk-btn.cancel:hover {
  background: #cbd5e0;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.content-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.published {
  background: #f0fff4;
  color: #22543d;
}

.status-badge.draft {
  background: #fffaf0;
  color: #744210;
}

.status-badge.archived {
  background: #fed7d7;
  color: #742a2a;
}

.content-image {
  height: 200px;
  overflow: hidden;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  font-size: 3rem;
  color: #cbd5e0;
}

.content-info {
  padding: 1rem;
}

.content-info h3 {
  color: #1a202c;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.content-description {
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.content-meta span {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.price {
  background: #e6fffa;
  color: #234e52;
}

.category {
  background: #fef5e7;
  color: #744210;
}

.sale-type {
  background: #e6f3ff;
  color: #2c5aa0;
}

.content-seller {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #4a5568;
}

.created-date {
  color: #718096;
  font-size: 0.75rem;
}

.content-actions {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn-approve,
.btn-reject,
.btn-feature,
.btn-view {
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
  min-width: 80px;
}

.btn-approve {
  background: #38a169;
  color: white;
}

.btn-approve:hover {
  background: #2f855a;
}

.btn-reject {
  background: #e53e3e;
  color: white;
}

.btn-reject:hover {
  background: #c53030;
}

.btn-feature {
  background: #d69e2e;
  color: white;
}

.btn-feature:hover {
  background: #b7791f;
}

.btn-feature.featured {
  background: #805ad5;
}

.btn-feature.featured:hover {
  background: #6b46c1;
}

.btn-view {
  background: #3182ce;
  color: white;
}

.btn-view:hover {
  background: #2c5aa0;
  text-decoration: none;
  color: white;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #4a5568;
  font-weight: 500;
}

/* No Content */
.no-content {
  text-align: center;
  padding: 3rem;
  color: #718096;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3182ce;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-content {
    padding: 1rem;
  }

  .admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .content-tabs {
    flex-direction: column;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .content-actions {
    flex-direction: column;
  }

  .content-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
