import api from './api';
import { 
  USER_ENDPOINTS, 
  CONTENT_ENDPOINTS, 
  ORDER_ENDPOINTS, 
  DASHBOARD_ENDPOINTS,
  SETTINGS_ENDPOINTS,
  CMS_ENDPOINTS,
  BID_ENDPOINTS,
  PAYMENT_ENDPOINTS,
  REVIEW_ENDPOINTS
} from '../utils/constants';

/**
 * Admin Service for comprehensive admin panel functionality
 */

// ============ USER MANAGEMENT ============

/**
 * Get all users with filtering and pagination
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with users data
 */
export const getUsers = async (params = {}) => {
  const response = await api.get(USER_ENDPOINTS.ALL, { params });
  return response.data;
};

/**
 * Get user statistics
 * @returns {Promise} Promise with user stats
 */
export const getUserStats = async () => {
  const response = await api.get(`${USER_ENDPOINTS.ALL}/stats`);
  return response.data;
};

/**
 * Get user activity
 * @param {string} userId - User ID
 * @returns {Promise} Promise with user activity data
 */
export const getUserActivity = async (userId) => {
  const response = await api.get(`${USER_ENDPOINTS.ALL}/activity/${userId}`);
  return response.data;
};

/**
 * Search users
 * @param {Object} params - Search parameters
 * @returns {Promise} Promise with search results
 */
export const searchUsers = async (params) => {
  const response = await api.get(`${USER_ENDPOINTS.ALL}/search`, { params });
  return response.data;
};

/**
 * Bulk update users
 * @param {Array} userIds - Array of user IDs
 * @param {Object} updates - Updates to apply
 * @returns {Promise} Promise with update result
 */
export const bulkUpdateUsers = async (userIds, updates) => {
  const response = await api.post(`${USER_ENDPOINTS.ALL}/bulk-update`, {
    userIds,
    updates
  });
  return response.data;
};

/**
 * Suspend user
 * @param {string} userId - User ID
 * @returns {Promise} Promise with result
 */
export const suspendUser = async (userId) => {
  const response = await api.put(`${USER_ENDPOINTS.ALL}/suspend/${userId}`);
  return response.data;
};

/**
 * Activate user
 * @param {string} userId - User ID
 * @returns {Promise} Promise with result
 */
export const activateUser = async (userId) => {
  const response = await api.put(`${USER_ENDPOINTS.ALL}/activate/${userId}`);
  return response.data;
};

// ============ CONTENT MANAGEMENT ============

/**
 * Get content statistics
 * @returns {Promise} Promise with content stats
 */
export const getContentStats = async () => {
  const response = await api.get(`${CONTENT_ENDPOINTS.BASE}/admin/stats`);
  return response.data;
};

/**
 * Get pending content for review
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with pending content
 */
export const getPendingContent = async (params = {}) => {
  const response = await api.get(`${CONTENT_ENDPOINTS.BASE}/admin/pending`, { params });
  return response.data;
};

/**
 * Get reported content
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with reported content
 */
export const getReportedContent = async (params = {}) => {
  const response = await api.get(`${CONTENT_ENDPOINTS.BASE}/admin/reported`, { params });
  return response.data;
};

/**
 * Approve content
 * @param {string} contentId - Content ID
 * @returns {Promise} Promise with result
 */
export const approveContent = async (contentId) => {
  const response = await api.put(`${CONTENT_ENDPOINTS.BASE}/admin/${contentId}/approve`);
  return response.data;
};

/**
 * Reject content
 * @param {string} contentId - Content ID
 * @param {string} reason - Rejection reason
 * @returns {Promise} Promise with result
 */
export const rejectContent = async (contentId, reason) => {
  const response = await api.put(`${CONTENT_ENDPOINTS.BASE}/admin/${contentId}/reject`, {
    reason
  });
  return response.data;
};

/**
 * Feature/unfeature content
 * @param {string} contentId - Content ID
 * @param {boolean} featured - Whether to feature the content
 * @returns {Promise} Promise with result
 */
export const featureContent = async (contentId, featured) => {
  const response = await api.put(`${CONTENT_ENDPOINTS.BASE}/admin/${contentId}/feature`, {
    featured
  });
  return response.data;
};

/**
 * Bulk update content
 * @param {Array} contentIds - Array of content IDs
 * @param {Object} updates - Updates to apply
 * @returns {Promise} Promise with update result
 */
export const bulkUpdateContent = async (contentIds, updates) => {
  const response = await api.post(`${CONTENT_ENDPOINTS.BASE}/admin/bulk-update`, {
    contentIds,
    updates
  });
  return response.data;
};

// ============ DASHBOARD & ANALYTICS ============

/**
 * Get admin dashboard statistics
 * @returns {Promise} Promise with dashboard stats
 */
export const getDashboardStats = async () => {
  const response = await api.get(DASHBOARD_ENDPOINTS.STATS);
  return response.data;
};

/**
 * Get user analytics
 * @returns {Promise} Promise with user analytics
 */
export const getUserAnalytics = async () => {
  const response = await api.get(DASHBOARD_ENDPOINTS.USERS);
  return response.data;
};

/**
 * Get content analytics
 * @returns {Promise} Promise with content analytics
 */
export const getContentAnalytics = async () => {
  const response = await api.get(DASHBOARD_ENDPOINTS.CONTENT);
  return response.data;
};

/**
 * Get order analytics
 * @returns {Promise} Promise with order analytics
 */
export const getOrderAnalytics = async () => {
  const response = await api.get(DASHBOARD_ENDPOINTS.ORDERS);
  return response.data;
};

/**
 * Get revenue analytics
 * @returns {Promise} Promise with revenue analytics
 */
export const getRevenueAnalytics = async () => {
  const response = await api.get(DASHBOARD_ENDPOINTS.REVENUE);
  return response.data;
};

/**
 * Get recent activity
 * @returns {Promise} Promise with recent activity
 */
export const getRecentActivity = async () => {
  const response = await api.get(DASHBOARD_ENDPOINTS.ACTIVITY);
  return response.data;
};

// ============ ORDER MANAGEMENT ============

/**
 * Get all orders (admin)
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with orders data
 */
export const getAllOrders = async (params = {}) => {
  const response = await api.get(ORDER_ENDPOINTS.ALL, { params });
  return response.data;
};

/**
 * Update order
 * @param {string} orderId - Order ID
 * @param {Object} updates - Updates to apply
 * @returns {Promise} Promise with updated order
 */
export const updateOrder = async (orderId, updates) => {
  const response = await api.put(ORDER_ENDPOINTS.SINGLE(orderId), updates);
  return response.data;
};

// ============ SETTINGS MANAGEMENT ============

/**
 * Get all settings
 * @returns {Promise} Promise with settings data
 */
export const getSettings = async () => {
  const response = await api.get(SETTINGS_ENDPOINTS.ALL);
  return response.data;
};

/**
 * Update multiple settings
 * @param {Object} settings - Settings to update
 * @returns {Promise} Promise with result
 */
export const updateSettings = async (settings) => {
  const response = await api.put(SETTINGS_ENDPOINTS.ALL, settings);
  return response.data;
};

/**
 * Create setting
 * @param {Object} setting - Setting data
 * @returns {Promise} Promise with created setting
 */
export const createSetting = async (setting) => {
  const response = await api.post(SETTINGS_ENDPOINTS.ALL, setting);
  return response.data;
};

/**
 * Delete setting
 * @param {string} settingId - Setting ID
 * @returns {Promise} Promise with result
 */
export const deleteSetting = async (settingId) => {
  const response = await api.delete(SETTINGS_ENDPOINTS.SINGLE(settingId));
  return response.data;
};

// ============ CMS MANAGEMENT ============

/**
 * Get all CMS pages
 * @returns {Promise} Promise with CMS pages
 */
export const getCMSPages = async () => {
  const response = await api.get(CMS_ENDPOINTS.ALL);
  return response.data;
};

/**
 * Create CMS page
 * @param {Object} pageData - Page data
 * @returns {Promise} Promise with created page
 */
export const createCMSPage = async (pageData) => {
  const response = await api.post(CMS_ENDPOINTS.ALL, pageData);
  return response.data;
};

/**
 * Update CMS page
 * @param {string} pageId - Page ID
 * @param {Object} updates - Updates to apply
 * @returns {Promise} Promise with updated page
 */
export const updateCMSPage = async (pageId, updates) => {
  const response = await api.put(CMS_ENDPOINTS.SINGLE(pageId), updates);
  return response.data;
};

/**
 * Delete CMS page
 * @param {string} pageId - Page ID
 * @returns {Promise} Promise with result
 */
export const deleteCMSPage = async (pageId) => {
  const response = await api.delete(CMS_ENDPOINTS.SINGLE(pageId));
  return response.data;
};

export default {
  // User Management
  getUsers,
  getUserStats,
  getUserActivity,
  searchUsers,
  bulkUpdateUsers,
  suspendUser,
  activateUser,
  
  // Content Management
  getContentStats,
  getPendingContent,
  getReportedContent,
  approveContent,
  rejectContent,
  featureContent,
  bulkUpdateContent,
  
  // Dashboard & Analytics
  getDashboardStats,
  getUserAnalytics,
  getContentAnalytics,
  getOrderAnalytics,
  getRevenueAnalytics,
  getRecentActivity,
  
  // Order Management
  getAllOrders,
  updateOrder,
  
  // Settings Management
  getSettings,
  updateSettings,
  createSetting,
  deleteSetting,
  
  // CMS Management
  getCMSPages,
  createCMSPage,
  updateCMSPage,
  deleteCMSPage,
};
